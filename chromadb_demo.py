"""
ChromaDB Demo - Alternative Vector Database
Simple embedded vector database for testing and development
"""

import numpy as np
import time
from typing import List, Dict, Any, Optional


def install_chromadb():
    """Install ChromaDB if not available"""
    try:
        import chromadb
        print("✅ ChromaDB already installed")
        return True
    except ImportError:
        print("📦 Installing ChromaDB...")
        import subprocess
        import sys
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'chromadb'], 
                         check=True, capture_output=True)
            print("✅ ChromaDB installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install ChromaDB: {e}")
            return False


class ChromaDBManager:
    """
    ChromaDB Manager for vector operations
    Simple alternative to Milvus for development and testing
    """
    
    def __init__(self, persist_directory: str = "./chroma_db"):
        """
        Initialize ChromaDB client
        
        Args:
            persist_directory: Directory to persist data
        """
        try:
            import chromadb
            from chromadb.config import Settings
            
            self.client = chromadb.PersistentClient(
                path=persist_directory,
                settings=Settings(anonymized_telemetry=False)
            )
            self.persist_directory = persist_directory
            print(f"✅ Connected to ChromaDB: {persist_directory}")
            
        except ImportError:
            print("❌ ChromaDB not installed. Installing...")
            if install_chromadb():
                import chromadb
                from chromadb.config import Settings
                
                self.client = chromadb.PersistentClient(
                    path=persist_directory,
                    settings=Settings(anonymized_telemetry=False)
                )
                self.persist_directory = persist_directory
                print(f"✅ Connected to ChromaDB: {persist_directory}")
            else:
                raise Exception("Failed to install ChromaDB")
    
    def create_collection(self, 
                         collection_name: str,
                         metadata: Optional[Dict] = None) -> bool:
        """
        Create a new collection
        
        Args:
            collection_name: Name of the collection
            metadata: Optional metadata for the collection
            
        Returns:
            bool: Success status
        """
        try:
            # Check if collection exists
            try:
                existing = self.client.get_collection(collection_name)
                print(f"⚠️  Collection '{collection_name}' already exists")
                return True
            except:
                pass
            
            # Create collection
            collection = self.client.create_collection(
                name=collection_name,
                metadata=metadata or {}
            )
            
            print(f"✅ Created collection '{collection_name}'")
            return True
            
        except Exception as e:
            print(f"❌ Error creating collection: {e}")
            return False
    
    def list_collections(self) -> List[str]:
        """
        List all collections
        
        Returns:
            List of collection names
        """
        try:
            collections = self.client.list_collections()
            collection_names = [col.name for col in collections]
            print(f"📋 Collections: {collection_names}")
            return collection_names
        except Exception as e:
            print(f"❌ Error listing collections: {e}")
            return []
    
    def get_collection_info(self, collection_name: str) -> Optional[Dict]:
        """
        Get collection information
        
        Args:
            collection_name: Name of the collection
            
        Returns:
            Collection information dict
        """
        try:
            collection = self.client.get_collection(collection_name)
            count = collection.count()
            
            info = {
                "name": collection_name,
                "count": count,
                "metadata": collection.metadata
            }
            
            print(f"📊 Collection '{collection_name}' info:")
            for key, value in info.items():
                print(f"   {key}: {value}")
            
            return info
            
        except Exception as e:
            print(f"❌ Error getting collection info: {e}")
            return None
    
    def insert_data(self, 
                   collection_name: str, 
                   documents: List[str],
                   embeddings: Optional[List[List[float]]] = None,
                   metadatas: Optional[List[Dict[str, str | int | float | bool | None]]] = None,
                   ids: Optional[List[str]] = None) -> bool:
        """
        Insert data into collection
        
        Args:
            collection_name: Name of the collection
            documents: List of documents
            embeddings: Optional pre-computed embeddings
            metadatas: Optional metadata for each document
            ids: Optional IDs for each document
            
        Returns:
            bool: Success status
        """
        try:
            collection = self.client.get_collection(collection_name)
            
            # Generate IDs if not provided
            if ids is None:
                existing_count = collection.count()
                ids = [f"doc_{existing_count + i}" for i in range(len(documents))]
            
            # Convert embeddings to numpy array if provided
            np_embeddings = None
            if embeddings is not None:
                np_embeddings = np.array(embeddings, dtype=np.float32)
            
            # Add documents
            collection.add(
                documents=documents,
                embeddings=np_embeddings,
                metadatas=metadatas,
                ids=ids
            )
            
            print(f"✅ Inserted {len(documents)} documents into '{collection_name}'")
            print(f"   Document IDs: {ids[:5]}..." if len(ids) > 5 else f"   Document IDs: {ids}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error inserting data: {e}")
            return False
    
    def search_documents(self, 
                        collection_name: str, 
                        query_texts: List[str], 
                        n_results: int = 10,
                        where: Optional[Dict] = None,
                        include: List[str] = None) -> Optional[Dict]:
        """
        Search for similar documents
        
        Args:
            collection_name: Name of the collection
            query_texts: List of query texts
            n_results: Number of results to return
            where: Filter conditions
            include: Fields to include in results
            
        Returns:
            Search results
        """
        try:
            collection = self.client.get_collection(collection_name)
            
            # Perform search
            start_time = time.time()
            results = collection.query(
                query_texts=query_texts,
                n_results=n_results,
                where=where,
                include=include or ["documents", "metadatas", "distances"]
            )
            search_time = time.time() - start_time
            
            print(f"🔍 Search completed in {search_time:.3f}s")
            if results.get('documents'):
                print(f"   Found {len(results['documents'][0])} results for first query")
            
            return results
            
        except Exception as e:
            print(f"❌ Error searching documents: {e}")
            return None
    
    def delete_collection(self, collection_name: str) -> bool:
        """
        Delete a collection
        
        Args:
            collection_name: Name of the collection to delete
            
        Returns:
            bool: Success status
        """
        try:
            self.client.delete_collection(collection_name)
            print(f"🗑️  Deleted collection '{collection_name}'")
            return True
            
        except Exception as e:
            print(f"❌ Error deleting collection: {e}")
            return False


def create_sample_documents(num_docs: int = 50) -> tuple:
    """
    Create sample documents for testing
    
    Args:
        num_docs: Number of documents to create
        
    Returns:
        Tuple of (documents, metadatas)
    """
    categories = ["technology", "science", "business", "health", "education"]
    topics = {
        "technology": ["AI", "machine learning", "software", "programming", "data science"],
        "science": ["physics", "chemistry", "biology", "astronomy", "research"],
        "business": ["marketing", "finance", "strategy", "management", "sales"],
        "health": ["medicine", "nutrition", "fitness", "wellness", "therapy"],
        "education": ["learning", "teaching", "curriculum", "students", "knowledge"]
    }
    
    documents = []
    metadatas = []
    
    for i in range(num_docs):
        category = categories[i % len(categories)]
        topic = np.random.choice(topics[category])
        
        doc = f"This is a document about {topic} in the {category} field. " \
              f"Document number {i+1} contains information about {topic} " \
              f"and related concepts in {category}."
        
        metadata = {
            "category": category,
            "topic": topic,
            "doc_id": int(i + 1),
            "score": float(np.random.uniform(0.1, 1.0)),
            "timestamp": int(time.time()) + i
        }
        
        documents.append(doc)
        metadatas.append(metadata)
    
    return documents, metadatas


def demo_chromadb_operations():
    """
    Demonstrate ChromaDB operations
    """
    print("🚀 Starting ChromaDB Demo")
    print("=" * 50)
    
    # Initialize ChromaDB manager
    chroma = ChromaDBManager()
    
    # Collection settings
    collection_name = "demo_collection"
    
    print(f"\n📋 Step 1: Create Collection")
    chroma.create_collection(collection_name, {"description": "Demo collection for testing"})
    
    print(f"\n📋 Step 2: List Collections")
    chroma.list_collections()
    
    print(f"\n📋 Step 3: Generate Sample Documents")
    documents, metadatas = create_sample_documents(30)
    print(f"   Generated {len(documents)} sample documents")
    
    print(f"\n📋 Step 4: Insert Documents")
    chroma.insert_data(collection_name, documents, metadatas=metadatas)
    
    print(f"\n📋 Step 5: Collection Info")
    chroma.get_collection_info(collection_name)
    
    print(f"\n📋 Step 6: Search Similar Documents")
    # Search for documents
    results = chroma.search_documents(
        collection_name=collection_name,
        query_texts=["artificial intelligence and machine learning"],
        n_results=5
    )
    
    if results and results.get('documents'):
        print("   Top 5 similar documents:")
        for i, (doc, metadata, distance) in enumerate(zip(
            results['documents'][0],
            results['metadatas'][0],
            results['distances'][0]
        )):
            print(f"   {i+1}. Category: {metadata.get('category', 'N/A')}, "
                  f"Topic: {metadata.get('topic', 'N/A')}, "
                  f"Distance: {distance:.4f}")
            print(f"      Text: {doc[:100]}...")
    
    print(f"\n📋 Step 7: Filtered Search")
    # Search with filter
    filtered_results = chroma.search_documents(
        collection_name=collection_name,
        query_texts=["programming and software development"],
        n_results=3,
        where={"category": "technology"}
    )
    
    if filtered_results and filtered_results.get('documents'):
        print("   Technology category results:")
        for i, (doc, metadata, distance) in enumerate(zip(
            filtered_results['documents'][0],
            filtered_results['metadatas'][0],
            filtered_results['distances'][0]
        )):
            print(f"   {i+1}. Topic: {metadata.get('topic', 'N/A')}, "
                  f"Distance: {distance:.4f}")
    
    print(f"\n📋 Step 8: Final Collection Info")
    chroma.get_collection_info(collection_name)
    
    print(f"\n🎉 ChromaDB Demo completed successfully!")
    print("=" * 50)


if __name__ == "__main__":
    demo_chromadb_operations()
