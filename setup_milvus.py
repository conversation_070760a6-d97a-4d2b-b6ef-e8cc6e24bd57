"""
Milvus Setup Script
Provides multiple ways to setup and run Milvus
"""

import subprocess
import sys
import time
import requests
from typing import Op<PERSON>


def check_docker():
    """Check if Docker is installed and running"""
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ Docker found: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Docker not found or not running")
        return False


def check_milvus_server(host: str = "localhost", port: int = 19530) -> bool:
    """Check if Milvus server is running"""
    try:
        # Try to connect to Milvus health check endpoint
        response = requests.get(f"http://{host}:9091/healthz", timeout=5)
        if response.status_code == 200:
            print(f"✅ Milvus server is running at {host}:{port}")
            return True
    except requests.exceptions.RequestException:
        pass
    
    print(f"❌ Milvus server not accessible at {host}:{port}")
    return False


def start_milvus_standalone():
    """Start Milvus standalone using Docker"""
    print("🚀 Starting Milvus standalone with Docker...")
    
    if not check_docker():
        print("Please install Docker first: https://docs.docker.com/get-docker/")
        return False
    
    try:
        # Download docker-compose file
        print("📥 Downloading Milvus docker-compose.yml...")
        
        docker_compose_content = """
version: '3.5'

services:
  etcd:
    container_name: milvus-etcd
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/volumes/etcd:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 20s
      retries: 3

  minio:
    container_name: milvus-minio
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9001:9001"
      - "9000:9000"
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/volumes/minio:/minio_data
    command: minio server /minio_data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  standalone:
    container_name: milvus-standalone
    image: milvusdb/milvus:v2.4.0
    command: ["milvus", "run", "standalone"]
    security_opt:
    - seccomp:unconfined
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/volumes/milvus:/var/lib/milvus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      - "etcd"
      - "minio"

networks:
  default:
    name: milvus
"""
        
        # Write docker-compose file
        with open("docker-compose.yml", "w") as f:
            f.write(docker_compose_content)
        
        print("📝 Created docker-compose.yml")
        
        # Start services
        print("🐳 Starting Milvus services...")
        result = subprocess.run(['docker-compose', 'up', '-d'], 
                              capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Failed to start Milvus: {result.stderr}")
            return False
        
        print("⏳ Waiting for Milvus to be ready...")
        
        # Wait for Milvus to be ready
        max_retries = 30
        for i in range(max_retries):
            if check_milvus_server():
                print("🎉 Milvus is ready!")
                return True
            
            print(f"   Waiting... ({i+1}/{max_retries})")
            time.sleep(10)
        
        print("❌ Milvus failed to start within timeout")
        return False
        
    except Exception as e:
        print(f"❌ Error starting Milvus: {e}")
        return False


def stop_milvus():
    """Stop Milvus services"""
    print("🛑 Stopping Milvus services...")
    try:
        result = subprocess.run(['docker-compose', 'down'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Milvus services stopped")
        else:
            print(f"❌ Error stopping services: {result.stderr}")
    except Exception as e:
        print(f"❌ Error: {e}")


def install_dependencies():
    """Install required Python dependencies"""
    print("📦 Installing Python dependencies...")
    
    dependencies = [
        "pymilvus>=2.5.0",
        "numpy",
        "requests"
    ]
    
    for dep in dependencies:
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                         check=True, capture_output=True)
            print(f"✅ Installed {dep}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {dep}: {e}")
            return False
    
    return True


def main():
    """Main setup function"""
    print("🔧 Milvus Setup Script")
    print("=" * 40)
    
    print("\nOptions:")
    print("1. Install Python dependencies")
    print("2. Start Milvus server (Docker)")
    print("3. Stop Milvus server")
    print("4. Check Milvus status")
    print("5. Full setup (dependencies + server)")
    
    choice = input("\nEnter your choice (1-5): ").strip()
    
    if choice == "1":
        install_dependencies()
    elif choice == "2":
        start_milvus_standalone()
    elif choice == "3":
        stop_milvus()
    elif choice == "4":
        check_milvus_server()
    elif choice == "5":
        print("🚀 Running full setup...")
        if install_dependencies():
            start_milvus_standalone()
    else:
        print("❌ Invalid choice")


if __name__ == "__main__":
    main()
