"""
Performance Testing for Vector Database Operations
Comprehensive benchmarking of vector database operations
"""

import time
import numpy as np
import statistics
from typing import List, Dict, Any, <PERSON><PERSON>
from chromadb_demo import ChromaDBManager, install_chromadb


class PerformanceTester:
    """
    Performance testing suite for vector database operations
    """
    
    def __init__(self, db_path: str = "./performance_test_db"):
        """Initialize performance tester"""
        self.chroma = ChromaDBManager(db_path)
        self.results = {}
        
    def generate_test_data(self, num_docs: int, doc_length: int = 100) -> Tuple[List[str], List[Dict]]:
        """
        Generate test data for performance testing
        
        Args:
            num_docs: Number of documents to generate
            doc_length: Average length of each document
            
        Returns:
            Tuple of (documents, metadatas)
        """
        documents = []
        metadatas = []
        
        categories = ["technology", "science", "business", "health", "education"]
        
        for i in range(num_docs):
            # Generate random text
            words = ["data", "analysis", "research", "development", "innovation", 
                    "technology", "science", "business", "health", "education",
                    "artificial", "intelligence", "machine", "learning", "algorithm",
                    "database", "vector", "search", "similarity", "embedding"]
            
            doc_words = np.random.choice(words, size=doc_length, replace=True)
            document = " ".join(doc_words)
            
            metadata = {
                "doc_id": f"DOC_{i+1:06d}",
                "category": categories[i % len(categories)],
                "length": len(document),
                "index": i
            }
            
            documents.append(document)
            metadatas.append(metadata)
            
        return documents, metadatas
    
    def measure_time(self, func, *args, **kwargs) -> Tuple[Any, float]:
        """
        Measure execution time of a function
        
        Args:
            func: Function to measure
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Tuple of (result, execution_time)
        """
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        return result, execution_time
    
    def test_collection_creation(self, collection_name: str) -> Dict[str, float]:
        """Test collection creation performance"""
        print(f"📋 Testing collection creation: {collection_name}")
        
        # Test collection creation
        _, creation_time = self.measure_time(
            self.chroma.create_collection,
            collection_name,
            {"test": "performance"}
        )
        
        print(f"   ✅ Collection created in {creation_time:.4f}s")
        
        return {"creation_time": creation_time}
    
    def test_data_insertion(self, collection_name: str, data_sizes: List[int]) -> Dict[str, List[float]]:
        """Test data insertion performance with different data sizes"""
        print(f"📥 Testing data insertion performance")
        
        insertion_times = []
        throughput_rates = []
        
        for size in data_sizes:
            print(f"   Testing with {size} documents...")
            
            # Generate test data
            documents, metadatas = self.generate_test_data(size)
            
            # Measure insertion time
            _, insertion_time = self.measure_time(
                self.chroma.insert_data,
                collection_name,
                documents,
                metadatas=metadatas
            )
            
            throughput = size / insertion_time if insertion_time > 0 else 0
            
            insertion_times.append(insertion_time)
            throughput_rates.append(throughput)
            
            print(f"      ✅ {size} docs inserted in {insertion_time:.4f}s "
                  f"({throughput:.2f} docs/sec)")
        
        return {
            "insertion_times": insertion_times,
            "throughput_rates": throughput_rates,
            "data_sizes": data_sizes
        }
    
    def test_search_performance(self, collection_name: str, 
                              num_queries: int = 100,
                              result_limits: List[int] = [1, 5, 10, 20]) -> Dict[str, Any]:
        """Test search performance with different parameters"""
        print(f"🔍 Testing search performance")
        
        # Generate query texts
        query_words = ["technology", "artificial intelligence", "data science", 
                      "machine learning", "research", "innovation", "development"]
        
        search_results = {}
        
        for limit in result_limits:
            print(f"   Testing with limit={limit}, {num_queries} queries...")
            
            search_times = []
            
            for i in range(num_queries):
                # Generate random query
                query_text = " ".join(np.random.choice(query_words, size=3, replace=True))
                
                # Measure search time
                _, search_time = self.measure_time(
                    self.chroma.search_documents,
                    collection_name,
                    [query_text],
                    n_results=limit
                )
                
                search_times.append(search_time)
            
            avg_time = statistics.mean(search_times)
            min_time = min(search_times)
            max_time = max(search_times)
            p95_time = np.percentile(search_times, 95)
            
            search_results[f"limit_{limit}"] = {
                "avg_time": avg_time,
                "min_time": min_time,
                "max_time": max_time,
                "p95_time": p95_time,
                "qps": 1.0 / avg_time if avg_time > 0 else 0
            }
            
            print(f"      ✅ Avg: {avg_time:.4f}s, P95: {p95_time:.4f}s, "
                  f"QPS: {1.0/avg_time:.2f}")
        
        return search_results
    
    def test_filtered_search(self, collection_name: str, num_queries: int = 50) -> Dict[str, float]:
        """Test filtered search performance"""
        print(f"🎯 Testing filtered search performance")
        
        categories = ["technology", "science", "business", "health", "education"]
        
        filtered_times = []
        unfiltered_times = []
        
        for i in range(num_queries):
            query_text = "artificial intelligence technology research"
            category = categories[i % len(categories)]
            
            # Measure unfiltered search
            _, unfiltered_time = self.measure_time(
                self.chroma.search_documents,
                collection_name,
                [query_text],
                n_results=10
            )
            
            # Measure filtered search
            _, filtered_time = self.measure_time(
                self.chroma.search_documents,
                collection_name,
                [query_text],
                n_results=10,
                where={"category": category}
            )
            
            unfiltered_times.append(unfiltered_time)
            filtered_times.append(filtered_time)
        
        avg_unfiltered = statistics.mean(unfiltered_times)
        avg_filtered = statistics.mean(filtered_times)
        
        print(f"   ✅ Unfiltered avg: {avg_unfiltered:.4f}s")
        print(f"   ✅ Filtered avg: {avg_filtered:.4f}s")
        print(f"   📊 Filter overhead: {((avg_filtered/avg_unfiltered - 1) * 100):.1f}%")
        
        return {
            "avg_unfiltered": avg_unfiltered,
            "avg_filtered": avg_filtered,
            "filter_overhead": (avg_filtered / avg_unfiltered - 1) * 100
        }
    
    def test_concurrent_operations(self, collection_name: str) -> Dict[str, float]:
        """Test concurrent operation performance simulation"""
        print(f"⚡ Testing concurrent operations simulation")
        
        # Simulate concurrent reads
        num_concurrent = 10
        query_text = "technology innovation research"
        
        start_time = time.time()
        
        # Sequential execution (simulating concurrent)
        for i in range(num_concurrent):
            self.chroma.search_documents(
                collection_name,
                [query_text],
                n_results=5
            )
        
        total_time = time.time() - start_time
        avg_time_per_query = total_time / num_concurrent
        
        print(f"   ✅ {num_concurrent} sequential queries in {total_time:.4f}s")
        print(f"   📊 Avg time per query: {avg_time_per_query:.4f}s")
        
        return {
            "total_time": total_time,
            "avg_time_per_query": avg_time_per_query,
            "simulated_qps": num_concurrent / total_time
        }
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive performance test suite"""
        print("🚀 Starting Comprehensive Performance Test")
        print("=" * 60)
        
        collection_name = "performance_test"
        
        # Test 1: Collection Creation
        creation_results = self.test_collection_creation(collection_name)
        
        # Test 2: Data Insertion with different sizes
        data_sizes = [100, 500, 1000, 2000]
        insertion_results = self.test_data_insertion(collection_name, data_sizes)
        
        # Test 3: Search Performance
        search_results = self.test_search_performance(collection_name)
        
        # Test 4: Filtered Search
        filter_results = self.test_filtered_search(collection_name)
        
        # Test 5: Concurrent Operations
        concurrent_results = self.test_concurrent_operations(collection_name)
        
        # Compile all results
        all_results = {
            "collection_creation": creation_results,
            "data_insertion": insertion_results,
            "search_performance": search_results,
            "filtered_search": filter_results,
            "concurrent_operations": concurrent_results
        }
        
        self.results = all_results
        return all_results
    
    def generate_report(self) -> str:
        """Generate performance test report"""
        if not self.results:
            return "No test results available. Run tests first."
        
        report = []
        report.append("📊 PERFORMANCE TEST REPORT")
        report.append("=" * 50)
        
        # Collection Creation
        creation = self.results.get("collection_creation", {})
        report.append(f"\n🏗️  Collection Creation:")
        report.append(f"   Time: {creation.get('creation_time', 0):.4f}s")
        
        # Data Insertion
        insertion = self.results.get("data_insertion", {})
        report.append(f"\n📥 Data Insertion Performance:")
        if insertion.get("data_sizes"):
            for i, size in enumerate(insertion["data_sizes"]):
                time_taken = insertion["insertion_times"][i]
                throughput = insertion["throughput_rates"][i]
                report.append(f"   {size:,} docs: {time_taken:.4f}s ({throughput:.2f} docs/sec)")
        
        # Search Performance
        search = self.results.get("search_performance", {})
        report.append(f"\n🔍 Search Performance:")
        for key, metrics in search.items():
            limit = key.replace("limit_", "")
            report.append(f"   Limit {limit}: Avg {metrics['avg_time']:.4f}s, "
                         f"P95 {metrics['p95_time']:.4f}s, QPS {metrics['qps']:.2f}")
        
        # Filtered Search
        filtered = self.results.get("filtered_search", {})
        report.append(f"\n🎯 Filtered Search:")
        report.append(f"   Unfiltered: {filtered.get('avg_unfiltered', 0):.4f}s")
        report.append(f"   Filtered: {filtered.get('avg_filtered', 0):.4f}s")
        report.append(f"   Overhead: {filtered.get('filter_overhead', 0):.1f}%")
        
        # Concurrent Operations
        concurrent = self.results.get("concurrent_operations", {})
        report.append(f"\n⚡ Concurrent Operations:")
        report.append(f"   Simulated QPS: {concurrent.get('simulated_qps', 0):.2f}")
        report.append(f"   Avg per query: {concurrent.get('avg_time_per_query', 0):.4f}s")
        
        return "\n".join(report)


def main():
    """Main function to run performance tests"""
    print("🔧 Vector Database Performance Testing")
    print("=" * 50)
    
    # Ensure ChromaDB is installed
    install_chromadb()
    
    # Initialize performance tester
    tester = PerformanceTester()
    
    # Run comprehensive tests
    results = tester.run_comprehensive_test()
    
    # Generate and display report
    print("\n" + tester.generate_report())
    
    print(f"\n🎉 Performance testing completed!")
    print("=" * 50)
    
    # Performance insights
    print("\n💡 Performance Insights:")
    
    insertion = results.get("data_insertion", {})
    if insertion.get("throughput_rates"):
        max_throughput = max(insertion["throughput_rates"])
        print(f"   📈 Max insertion throughput: {max_throughput:.2f} docs/sec")
    
    search = results.get("search_performance", {})
    if search.get("limit_10"):
        qps = search["limit_10"]["qps"]
        print(f"   🔍 Search QPS (limit=10): {qps:.2f}")
    
    filtered = results.get("filtered_search", {})
    if filtered.get("filter_overhead"):
        overhead = filtered["filter_overhead"]
        print(f"   🎯 Filter overhead: {overhead:.1f}%")
    
    print("\n📋 Recommendations:")
    print("   • Use appropriate batch sizes for insertion")
    print("   • Consider result limits based on use case")
    print("   • Monitor filter performance for complex queries")
    print("   • Plan for concurrent access patterns")


if __name__ == "__main__":
    main()
