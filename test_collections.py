"""
Test Collections Demo
Create and test 2 different collections with various data types and use cases
"""

import numpy as np
import time
from typing import List, Dict, Any
from chromadb_demo import ChromaDBManager, install_chromadb


def create_product_collection_data(num_products: int = 100) -> tuple:
    """
    Create sample e-commerce product data
    
    Args:
        num_products: Number of products to create
        
    Returns:
        Tuple of (documents, metadatas)
    """
    categories = ["electronics", "clothing", "books", "home", "sports"]
    brands = ["Apple", "Samsung", "Nike", "Adidas", "Sony", "LG", "Dell", "HP"]
    
    documents = []
    metadatas = []
    
    for i in range(num_products):
        category = categories[i % len(categories)]
        brand = brands[i % len(brands)]
        
        # Generate product description
        if category == "electronics":
            products = ["smartphone", "laptop", "tablet", "headphones", "camera"]
            product_type = products[i % len(products)]
            doc = f"{brand} {product_type} with advanced features. " \
                  f"High-quality {category} product with excellent performance. " \
                  f"Model {product_type}-{i+1} offers great value for money."
        elif category == "clothing":
            items = ["t-shirt", "jeans", "jacket", "shoes", "dress"]
            item_type = items[i % len(items)]
            doc = f"{brand} {item_type} made from premium materials. " \
                  f"Comfortable and stylish {category} item. " \
                  f"Perfect for casual and formal occasions."
        elif category == "books":
            genres = ["fiction", "science", "history", "technology", "biography"]
            genre = genres[i % len(genres)]
            doc = f"Bestselling {genre} book by renowned author. " \
                  f"Engaging content about {genre} topics. " \
                  f"Highly rated book with excellent reviews."
        elif category == "home":
            items = ["furniture", "decoration", "kitchen", "bathroom", "lighting"]
            item_type = items[i % len(items)]
            doc = f"Premium {item_type} for modern homes. " \
                  f"High-quality {category} product with elegant design. " \
                  f"Durable and functional {item_type} solution."
        else:  # sports
            items = ["equipment", "clothing", "shoes", "accessories", "gear"]
            item_type = items[i % len(items)]
            doc = f"{brand} sports {item_type} for athletes. " \
                  f"Professional-grade {category} product. " \
                  f"Designed for performance and comfort."
        
        metadata = {
            "product_id": f"PROD_{i+1:04d}",
            "category": category,
            "brand": brand,
            "price": round(np.random.uniform(10.0, 1000.0), 2),
            "rating": round(np.random.uniform(3.0, 5.0), 1),
            "in_stock": "yes" if np.random.choice([True, False], p=[0.8, 0.2]) else "no",
            "created_date": f"2024-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}"
        }
        
        documents.append(doc)
        metadatas.append(metadata)
    
    return documents, metadatas


def create_article_collection_data(num_articles: int = 80) -> tuple:
    """
    Create sample news/blog article data
    
    Args:
        num_articles: Number of articles to create
        
    Returns:
        Tuple of (documents, metadatas)
    """
    topics = ["artificial intelligence", "climate change", "space exploration", 
              "renewable energy", "quantum computing", "biotechnology", 
              "cybersecurity", "blockchain", "robotics", "virtual reality"]
    
    authors = ["Dr. Sarah Johnson", "Prof. Michael Chen", "Alex Rodriguez", 
               "Dr. Emily Watson", "James Thompson", "Dr. Lisa Park", 
               "Robert Kim", "Dr. Maria Garcia"]
    
    sources = ["TechNews", "ScienceDaily", "FutureTech", "ResearchToday", 
               "InnovationHub", "TechReview", "ScienceWorld", "DigitalTrends"]
    
    documents = []
    metadatas = []
    
    for i in range(num_articles):
        topic = topics[i % len(topics)]
        author = authors[i % len(authors)]
        source = sources[i % len(sources)]
        
        # Generate article content
        doc = f"Latest developments in {topic} show promising results. " \
              f"Researchers have made significant breakthroughs in {topic} technology. " \
              f"This advancement could revolutionize how we approach {topic}. " \
              f"Industry experts believe {topic} will play a crucial role in the future. " \
              f"The implications of {topic} research extend beyond current applications."
        
        metadata = {
            "article_id": f"ART_{i+1:04d}",
            "title": f"Breakthrough in {topic.title()}: New Research Findings",
            "author": author,
            "source": source,
            "topic": topic,
            "word_count": int(np.random.randint(500, 2000)),
            "views": int(np.random.randint(100, 10000)),
            "likes": int(np.random.randint(10, 500)),
            "published_date": f"2024-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}",
            "is_featured": "yes" if np.random.choice([True, False], p=[0.2, 0.8]) else "no"
        }
        
        documents.append(doc)
        metadatas.append(metadata)
    
    return documents, metadatas


def test_product_collection():
    """Test e-commerce product collection"""
    print("🛍️  Testing Product Collection")
    print("-" * 40)
    
    # Initialize ChromaDB
    chroma = ChromaDBManager("./test_collections_db")
    
    # Create product collection
    collection_name = "products"
    chroma.create_collection(collection_name, {"type": "e-commerce", "description": "Product catalog"})
    
    # Generate and insert product data
    print("📦 Generating product data...")
    documents, metadatas = create_product_collection_data(100)
    chroma.insert_data(collection_name, documents, metadatas=metadatas)
    
    # Get collection info
    chroma.get_collection_info(collection_name)
    
    # Test various searches
    print("\n🔍 Testing Product Searches:")
    
    # 1. Search for electronics
    print("\n1. Search for smartphones:")
    results = chroma.search_documents(
        collection_name=collection_name,
        query_texts=["smartphone mobile phone technology"],
        n_results=3
    )
    
    if results and results.get('documents'):
        for i, (doc, metadata, distance) in enumerate(zip(
            results['documents'][0],
            results['metadatas'][0],
            results['distances'][0]
        )):
            print(f"   {i+1}. {metadata.get('brand', 'N/A')} - ${metadata.get('price', 'N/A')} "
                  f"(Rating: {metadata.get('rating', 'N/A')}) - Distance: {distance:.4f}")
    
    # 2. Filter by category and price
    print("\n2. Electronics under $500:")
    results = chroma.search_documents(
        collection_name=collection_name,
        query_texts=["high quality electronics technology"],
        n_results=5,
        where={"category": "electronics"}
    )
    
    if results and results.get('documents'):
        for i, (doc, metadata, distance) in enumerate(zip(
            results['documents'][0],
            results['metadatas'][0],
            results['distances'][0]
        )):
            print(f"   {i+1}. {metadata.get('product_id', 'N/A')} - "
                  f"{metadata.get('brand', 'N/A')} - ${metadata.get('price', 'N/A')}")
    
    # 3. High-rated products
    print("\n3. High-rated products (>4.5):")
    results = chroma.search_documents(
        collection_name=collection_name,
        query_texts=["premium quality excellent product"],
        n_results=3,
        where={"category": "electronics"}
    )
    
    if results and results.get('documents'):
        for i, (doc, metadata, distance) in enumerate(zip(
            results['documents'][0],
            results['metadatas'][0],
            results['distances'][0]
        )):
            print(f"   {i+1}. {metadata.get('category', 'N/A')} - "
                  f"Rating: {metadata.get('rating', 'N/A')} - "
                  f"${metadata.get('price', 'N/A')}")
    
    return collection_name


def test_article_collection():
    """Test news/blog article collection"""
    print("\n📰 Testing Article Collection")
    print("-" * 40)
    
    # Initialize ChromaDB (reuse existing client)
    chroma = ChromaDBManager("./test_collections_db")
    
    # Create article collection
    collection_name = "articles"
    chroma.create_collection(collection_name, {"type": "content", "description": "News and blog articles"})
    
    # Generate and insert article data
    print("📝 Generating article data...")
    documents, metadatas = create_article_collection_data(80)
    chroma.insert_data(collection_name, documents, metadatas=metadatas)
    
    # Get collection info
    chroma.get_collection_info(collection_name)
    
    # Test various searches
    print("\n🔍 Testing Article Searches:")
    
    # 1. Search for AI articles
    print("\n1. AI and machine learning articles:")
    results = chroma.search_documents(
        collection_name=collection_name,
        query_texts=["artificial intelligence machine learning AI technology"],
        n_results=3
    )
    
    if results and results.get('documents'):
        for i, (doc, metadata, distance) in enumerate(zip(
            results['documents'][0],
            results['metadatas'][0],
            results['distances'][0]
        )):
            print(f"   {i+1}. {metadata.get('title', 'N/A')}")
            print(f"      Author: {metadata.get('author', 'N/A')} | "
                  f"Views: {metadata.get('views', 'N/A')} | "
                  f"Distance: {distance:.4f}")
    
    # 2. Popular articles (high views)
    print("\n2. Popular articles (>5000 views):")
    results = chroma.search_documents(
        collection_name=collection_name,
        query_texts=["breakthrough research innovation technology"],
        n_results=3,
        where={"topic": "artificial intelligence"}
    )
    
    if results and results.get('documents'):
        for i, (doc, metadata, distance) in enumerate(zip(
            results['documents'][0],
            results['metadatas'][0],
            results['distances'][0]
        )):
            print(f"   {i+1}. {metadata.get('topic', 'N/A')} - "
                  f"Views: {metadata.get('views', 'N/A')} - "
                  f"Likes: {metadata.get('likes', 'N/A')}")
    
    # 3. Featured articles by specific author
    print("\n3. Featured articles:")
    results = chroma.search_documents(
        collection_name=collection_name,
        query_texts=["important research scientific discovery"],
        n_results=3,
        where={"is_featured": "yes"}
    )
    
    if results and results.get('documents'):
        for i, (doc, metadata, distance) in enumerate(zip(
            results['documents'][0],
            results['metadatas'][0],
            results['distances'][0]
        )):
            print(f"   {i+1}. {metadata.get('title', 'N/A')}")
            print(f"      Source: {metadata.get('source', 'N/A')} | "
                  f"Date: {metadata.get('published_date', 'N/A')}")
    
    return collection_name


def cross_collection_analysis(product_collection: str, article_collection: str):
    """Perform analysis across both collections"""
    print("\n🔄 Cross-Collection Analysis")
    print("-" * 40)
    
    chroma = ChromaDBManager("./test_collections_db")
    
    # Get collection stats
    print("📊 Collection Statistics:")
    product_info = chroma.get_collection_info(product_collection)
    article_info = chroma.get_collection_info(article_collection)
    
    print(f"\nTotal Products: {product_info.get('count', 0) if product_info else 0}")
    print(f"Total Articles: {article_info.get('count', 0) if article_info else 0}")
    
    # Technology-related search across both collections
    print("\n🔍 Technology-related content across collections:")
    
    # Search products for tech items
    tech_products = chroma.search_documents(
        collection_name=product_collection,
        query_texts=["technology innovation electronic digital smart"],
        n_results=2,
        where={"category": "electronics"}
    )
    
    # Search articles for tech content
    tech_articles = chroma.search_documents(
        collection_name=article_collection,
        query_texts=["technology innovation electronic digital smart"],
        n_results=2
    )
    
    print("\nTech Products:")
    if tech_products and tech_products.get('documents'):
        for i, metadata in enumerate(tech_products['metadatas'][0]):
            print(f"   {i+1}. {metadata.get('brand', 'N/A')} - "
                  f"${metadata.get('price', 'N/A')} - "
                  f"Rating: {metadata.get('rating', 'N/A')}")
    
    print("\nTech Articles:")
    if tech_articles and tech_articles.get('documents'):
        for i, metadata in enumerate(tech_articles['metadatas'][0]):
            print(f"   {i+1}. {metadata.get('topic', 'N/A')} - "
                  f"Views: {metadata.get('views', 'N/A')}")


def main():
    """Main function to run all collection tests"""
    print("🚀 Starting Test Collections Demo")
    print("=" * 50)
    
    # Ensure ChromaDB is installed
    install_chromadb()
    
    # Test product collection
    product_collection = test_product_collection()
    
    # Test article collection  
    article_collection = test_article_collection()
    
    # Cross-collection analysis
    cross_collection_analysis(product_collection, article_collection)
    
    print(f"\n🎉 Test Collections Demo completed successfully!")
    print("=" * 50)
    print("\n💡 Key Features Demonstrated:")
    print("   ✅ Multiple collection management")
    print("   ✅ Different data types and schemas")
    print("   ✅ Complex filtering with metadata")
    print("   ✅ Semantic search across domains")
    print("   ✅ Cross-collection analysis")
    print("   ✅ Real-world use case scenarios")


if __name__ == "__main__":
    main()
