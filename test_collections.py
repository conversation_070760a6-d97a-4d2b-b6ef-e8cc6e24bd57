"""
Demo đơn giản về ChromaDB - Tạo và test 2 collections
<PERSON><PERSON> hợp cho người mới học
"""

from chromadb_demo import ChromaDBManager, install_chromadb


def create_simple_products():
    """Tạo dữ liệu sản phẩm đơn giản"""
    # <PERSON><PERSON> sách sản phẩm đơn giản
    products = [
        "iPhone 15 - Điện thoại thông minh cao cấp của Apple",
        "Samsung Galaxy S24 - Smartphone Android mạnh mẽ",
        "MacBook Air - Laptop mỏng nhẹ cho sinh viên",
        "Dell XPS 13 - Laptop Windows hiệu năng cao",
        "AirPods Pro - Tai nghe không dây chống ồn",
        "Sony WH-1000XM5 - Tai nghe over-ear chất lượng cao",
        "iPad Air - Máy tính bảng đa năng",
        "Surface Pro 9 - Tablet 2-in-1 của Microsoft"
    ]

    # Metadata đơn giản cho mỗi sản phẩm
    metadatas = [
        {"category": "phone", "brand": "Apple", "price": 999},
        {"category": "phone", "brand": "Samsung", "price": 899},
        {"category": "laptop", "brand": "Apple", "price": 1199},
        {"category": "laptop", "brand": "Dell", "price": 1099},
        {"category": "headphone", "brand": "Apple", "price": 249},
        {"category": "headphone", "brand": "Sony", "price": 399},
        {"category": "tablet", "brand": "Apple", "price": 599},
        {"category": "tablet", "brand": "Microsoft", "price": 999}
    ]

    return products, metadatas


def create_simple_articles():
    """Tạo dữ liệu bài viết đơn giản"""
    # Danh sách bài viết đơn giản
    articles = [
        "Trí tuệ nhân tạo đang thay đổi cách chúng ta làm việc",
        "Xe điện Tesla ra mắt mẫu xe mới với pin bền hơn",
        "Công nghệ blockchain giúp bảo mật dữ liệu tốt hơn",
        "Robot mới có thể giúp việc nhà như con người",
        "Ứng dụng di động giúp học tiếng Anh hiệu quả"
    ]

    # Metadata đơn giản cho mỗi bài viết
    metadatas = [
        {"topic": "AI", "author": "Nguyễn Văn A"},
        {"topic": "Technology", "author": "Trần Thị B"},
        {"topic": "Blockchain", "author": "Lê Văn C"},
        {"topic": "Robot", "author": "Phạm Thị D"},
        {"topic": "Education", "author": "Hoàng Văn E"}
    ]

    return articles, metadatas


def test_products():
    """Test collection sản phẩm - đơn giản"""
    print("🛍️ Test Collection Sản Phẩm")
    print("-" * 30)

    # Bước 1: Tạo ChromaDB manager
    chroma = ChromaDBManager("./simple_db")

    # Bước 2: Tạo collection cho sản phẩm
    chroma.create_collection("products", {"description": "Danh sách sản phẩm"})

    # Bước 3: Thêm dữ liệu sản phẩm
    print("📦 Thêm dữ liệu sản phẩm...")
    products, metadatas = create_simple_products()
    chroma.insert_data("products", products, metadatas=metadatas)

    # Bước 4: Xem thông tin collection
    chroma.get_collection_info("products")

    # Bước 5: Test tìm kiếm
    print("\n🔍 Test tìm kiếm:")

    # Tìm điện thoại
    print("\n1. Tìm điện thoại:")
    results = chroma.search_documents(
        collection_name="products",
        query_texts=["điện thoại smartphone"],
        n_results=2
    )

    if results and results.get('documents'):
        for i, (doc, metadata) in enumerate(zip(
            results['documents'][0],
            results['metadatas'][0]
        )):
            print(f"   {i+1}. {metadata.get('brand')} - ${metadata.get('price')}")

    # Tìm laptop
    print("\n2. Tìm laptop:")
    results = chroma.search_documents(
        collection_name="products",
        query_texts=["laptop máy tính"],
        n_results=2
    )

    if results and results.get('documents'):
        for i, (doc, metadata) in enumerate(zip(
            results['documents'][0],
            results['metadatas'][0]
        )):
            print(f"   {i+1}. {metadata.get('brand')} - ${metadata.get('price')}")

    return "products"


def test_articles():
    """Test collection bài viết - đơn giản"""
    print("\n📰 Test Collection Bài Viết")
    print("-" * 30)

    # Sử dụng lại ChromaDB manager
    chroma = ChromaDBManager("./simple_db")

    # Tạo collection cho bài viết
    chroma.create_collection("articles", {"description": "Danh sách bài viết"})

    # Thêm dữ liệu bài viết
    print("📝 Thêm dữ liệu bài viết...")
    articles, metadatas = create_simple_articles()
    chroma.insert_data("articles", articles, metadatas=metadatas)

    # Xem thông tin collection
    chroma.get_collection_info("articles")

    # Test tìm kiếm
    print("\n🔍 Test tìm kiếm:")

    # Tìm bài viết về AI
    print("\n1. Tìm bài viết về AI:")
    results = chroma.search_documents(
        collection_name="articles",
        query_texts=["trí tuệ nhân tạo AI"],
        n_results=2
    )

    if results and results.get('documents'):
        for i, (doc, metadata) in enumerate(zip(
            results['documents'][0],
            results['metadatas'][0]
        )):
            print(f"   {i+1}. Chủ đề: {metadata.get('topic')} - Tác giả: {metadata.get('author')}")

    # Tìm bài viết về công nghệ
    print("\n2. Tìm bài viết về công nghệ:")
    results = chroma.search_documents(
        collection_name="articles",
        query_texts=["công nghệ technology"],
        n_results=2
    )

    if results and results.get('documents'):
        for i, (doc, metadata) in enumerate(zip(
            results['documents'][0],
            results['metadatas'][0]
        )):
            print(f"   {i+1}. Chủ đề: {metadata.get('topic')} - Tác giả: {metadata.get('author')}")

    return "articles"


def main():
    """Hàm chính - chạy tất cả demo"""
    print("🚀 Demo ChromaDB Đơn Giản")
    print("=" * 30)

    # Bước 1: Cài đặt ChromaDB
    print("📦 Cài đặt ChromaDB...")
    install_chromadb()

    # Bước 2: Test collection sản phẩm
    product_collection = test_products()

    # Bước 3: Test collection bài viết
    article_collection = test_articles()

    # Bước 4: Tổng kết
    print(f"\n🎉 Demo hoàn thành!")
    print("=" * 30)
    print("\n💡 Những gì đã học:")
    print("   ✅ Tạo ChromaDB collection")
    print("   ✅ Thêm dữ liệu vào collection")
    print("   ✅ Tìm kiếm semantic search")
    print("   ✅ Làm việc với metadata")
    print("   ✅ Quản lý nhiều collections")


if __name__ == "__main__":
    main()
