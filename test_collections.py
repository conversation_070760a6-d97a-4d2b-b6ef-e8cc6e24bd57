"""
Demo siêu đơn giản về ChromaDB
Chỉ 3 bước: Tạo → Thêm → Tìm
"""

from chromadb_demo import ChromaDBManager, install_chromadb


def demo_co_ban():
    """Demo cơ bản nhất - chỉ 3 bước"""
    print("🚀 Demo ChromaDB Siêu Đơn Giản")
    print("=" * 35)

    # Cài đặt ChromaDB
    install_chromadb()

    # Tạo ChromaDB
    chroma = ChromaDBManager("./my_db")

    # BƯỚC 1: TẠO COLLECTION
    print("\n📁 BƯỚC 1: Tạo collection")
    chroma.create_collection("my_data", {"description": "Dữ liệu của tôi"})

    # BƯỚC 2: THÊM DỮ LIỆU
    print("\n📝 BƯỚC 2: Thêm dữ liệu")

    # Dữ liệu đơn giản
    documents = [
        "iPhone là điện thoại của Apple",
        "Samsung Galaxy là điện thoại Android",
        "MacBook là laptop của Apple"
    ]

    # Thông tin thêm (metadata)
    metadatas = [
        {"type": "phone", "brand": "Apple"},
        {"type": "phone", "brand": "Samsung"},
        {"type": "laptop", "brand": "Apple"}
    ]

    # Thêm vào database
    chroma.insert_data("my_data", documents, metadatas=metadatas)
    print("✅ Đã thêm 3 sản phẩm")

    # BƯỚC 3: TÌM KIẾM
    print("\n🔍 BƯỚC 3: Tìm kiếm")

    # Tìm điện thoại
    print("\n→ Tìm: 'điện thoại'")
    results = chroma.search_documents(
        collection_name="my_data",
        query_texts=["điện thoại"],
        n_results=2
    )

    # Hiển thị kết quả
    if results and results.get('documents'):
        for i, (doc, metadata) in enumerate(zip(
            results['documents'][0],
            results['metadatas'][0]
        )):
            print(f"   {i+1}. {metadata.get('brand')} ({metadata.get('type')})")

    # Tìm Apple
    print("\n→ Tìm: 'Apple'")
    results = chroma.search_documents(
        collection_name="my_data",
        query_texts=["Apple"],
        n_results=2
    )

    if results and results.get('documents'):
        for i, (doc, metadata) in enumerate(zip(
            results['documents'][0],
            results['metadatas'][0]
        )):
            print(f"   {i+1}. {metadata.get('brand')} {metadata.get('type')}")

    print(f"\n🎉 Xong! Đã học cách dùng ChromaDB!")


if __name__ == "__main__":
    demo_co_ban()
