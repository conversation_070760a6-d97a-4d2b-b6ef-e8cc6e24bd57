"""
Milvus Vector Database Demo
Comprehensive example showing all basic operations
"""

import numpy as np
import time
from typing import List, Dict, Any, Optional
from pymilvus import MilvusClient, DataType


class MilvusManager:
    """
    Milvus Database Manager
    Provides comprehensive vector database operations
    """

    def __init__(self, uri: str = "http://localhost:19530"):
        """
        Initialize Milvus client

        Args:
            uri: Milvus server URI (default: localhost:19530)
                 For embedded mode, use "./milvus_demo.db" (requires milvus-lite)
        """
        try:
            self.client = MilvusClient(uri=uri)
            self.uri = uri
            print(f"✅ Connected to Milvus: {uri}")
        except Exception as e:
            print(f"❌ Failed to connect to Milvus at {uri}")
            print(f"   Error: {e}")
            print(f"   💡 Make sure Milvus server is running or use embedded mode")
            raise

    def create_collection(self,
                         collection_name: str,
                         dimension: int = 768,
                         metric_type: str = "COSINE",
                         auto_id: bool = True) -> bool:
        """
        Create a new collection

        Args:
            collection_name: Name of the collection
            dimension: Vector dimension
            metric_type: Distance metric (COSINE, L2, IP)
            auto_id: Whether to auto-generate IDs

        Returns:
            bool: Success status
        """
        try:
            # Check if collection exists
            if self.client.has_collection(collection_name):
                print(f"⚠️  Collection '{collection_name}' already exists")
                return True

            # Create collection
            self.client.create_collection(
                collection_name=collection_name,
                dimension=dimension,
                metric_type=metric_type,
                auto_id=auto_id
            )

            print(f"✅ Created collection '{collection_name}' with dimension {dimension}")
            return True

        except Exception as e:
            print(f"❌ Error creating collection: {e}")
            return False

    def list_collections(self) -> List[str]:
        """
        List all collections

        Returns:
            List of collection names
        """
        try:
            collections = self.client.list_collections()
            print(f"📋 Collections: {collections}")
            return collections
        except Exception as e:
            print(f"❌ Error listing collections: {e}")
            return []

    def get_collection_info(self, collection_name: str) -> Optional[Dict]:
        """
        Get collection information

        Args:
            collection_name: Name of the collection

        Returns:
            Collection information dict
        """
        try:
            if not self.client.has_collection(collection_name):
                print(f"❌ Collection '{collection_name}' does not exist")
                return None

            # Get collection stats
            stats = self.client.get_collection_stats(collection_name)
            print(f"📊 Collection '{collection_name}' stats:")
            for key, value in stats.items():
                print(f"   {key}: {value}")

            return stats

        except Exception as e:
            print(f"❌ Error getting collection info: {e}")
            return None

    def insert_data(self,
                   collection_name: str,
                   data: List[Dict[str, Any]]) -> bool:
        """
        Insert data into collection

        Args:
            collection_name: Name of the collection
            data: List of data dictionaries with 'vector' and optional metadata

        Returns:
            bool: Success status
        """
        try:
            if not self.client.has_collection(collection_name):
                print(f"❌ Collection '{collection_name}' does not exist")
                return False

            # Insert data
            result = self.client.insert(
                collection_name=collection_name,
                data=data
            )

            print(f"✅ Inserted {len(data)} vectors into '{collection_name}'")
            print(f"   Insert IDs: {result['ids'][:5]}..." if len(result['ids']) > 5 else f"   Insert IDs: {result['ids']}")

            return True

        except Exception as e:
            print(f"❌ Error inserting data: {e}")
            return False

    def search_vectors(self,
                      collection_name: str,
                      query_vectors: List[List[float]],
                      limit: int = 10,
                      filter_expr: Optional[str] = None,
                      output_fields: Optional[List[str]] = None) -> Optional[List[List[Dict]]]:
        """
        Search for similar vectors

        Args:
            collection_name: Name of the collection
            query_vectors: List of query vectors
            limit: Number of results to return
            filter_expr: Filter expression
            output_fields: Fields to return in results

        Returns:
            Search results
        """
        try:
            if not self.client.has_collection(collection_name):
                print(f"❌ Collection '{collection_name}' does not exist")
                return None

            # Perform search
            start_time = time.time()
            search_params = {
                "collection_name": collection_name,
                "data": query_vectors,
                "limit": limit,
                "output_fields": output_fields or []
            }

            if filter_expr:
                search_params["filter"] = filter_expr

            results = self.client.search(**search_params)
            search_time = time.time() - start_time

            print(f"🔍 Search completed in {search_time:.3f}s")
            print(f"   Found {len(results[0])} results for first query")

            return results

        except Exception as e:
            print(f"❌ Error searching vectors: {e}")
            return None

    def delete_data(self,
                   collection_name: str,
                   filter_expr: str) -> bool:
        """
        Delete data from collection

        Args:
            collection_name: Name of the collection
            filter_expr: Filter expression for deletion

        Returns:
            bool: Success status
        """
        try:
            if not self.client.has_collection(collection_name):
                print(f"❌ Collection '{collection_name}' does not exist")
                return False

            # Delete data
            self.client.delete(
                collection_name=collection_name,
                filter=filter_expr
            )

            print(f"🗑️  Deleted data with filter: {filter_expr}")
            return True

        except Exception as e:
            print(f"❌ Error deleting data: {e}")
            return False

    def drop_collection(self, collection_name: str) -> bool:
        """
        Drop a collection

        Args:
            collection_name: Name of the collection to drop

        Returns:
            bool: Success status
        """
        try:
            if not self.client.has_collection(collection_name):
                print(f"⚠️  Collection '{collection_name}' does not exist")
                return True

            self.client.drop_collection(collection_name)
            print(f"🗑️  Dropped collection '{collection_name}'")
            return True

        except Exception as e:
            print(f"❌ Error dropping collection: {e}")
            return False


def generate_random_vectors(num_vectors: int, dimension: int) -> List[List[float]]:
    """
    Generate random vectors for testing

    Args:
        num_vectors: Number of vectors to generate
        dimension: Vector dimension

    Returns:
        List of random vectors
    """
    vectors = []
    for _ in range(num_vectors):
        # Generate random vector and normalize
        vector = np.random.random(dimension).astype(np.float32)
        # Normalize to unit vector
        vector = vector / np.linalg.norm(vector)
        vectors.append(vector.tolist())

    return vectors


def create_sample_data(num_items: int, dimension: int = 768) -> List[Dict[str, Any]]:
    """
    Create sample data with vectors and metadata

    Args:
        num_items: Number of data items to create
        dimension: Vector dimension

    Returns:
        List of data dictionaries
    """
    data = []
    vectors = generate_random_vectors(num_items, dimension)

    categories = ["technology", "science", "business", "health", "education"]

    for i, vector in enumerate(vectors):
        item = {
            "vector": vector,
            "id": i + 1,
            "title": f"Document {i + 1}",
            "category": categories[i % len(categories)],
            "score": np.random.uniform(0.1, 1.0),
            "timestamp": int(time.time()) + i
        }
        data.append(item)

    return data


def demo_basic_operations():
    """
    Demonstrate basic Milvus operations
    """
    print("🚀 Starting Milvus Demo")
    print("=" * 50)

    # Initialize Milvus manager
    milvus = MilvusManager()

    # Collection settings
    collection_name = "demo_collection"
    dimension = 128  # Smaller dimension for demo

    print(f"\n📋 Step 1: Create Collection")
    milvus.create_collection(collection_name, dimension)

    print(f"\n📋 Step 2: List Collections")
    milvus.list_collections()

    print(f"\n📋 Step 3: Generate Sample Data")
    sample_data = create_sample_data(100, dimension)
    print(f"   Generated {len(sample_data)} sample items")

    print(f"\n📋 Step 4: Insert Data")
    milvus.insert_data(collection_name, sample_data)

    print(f"\n📋 Step 5: Collection Info")
    milvus.get_collection_info(collection_name)

    print(f"\n📋 Step 6: Search Similar Vectors")
    # Generate query vector
    query_vectors = generate_random_vectors(1, dimension)
    results = milvus.search_vectors(
        collection_name=collection_name,
        query_vectors=query_vectors,
        limit=5,
        output_fields=["title", "category", "score"]
    )

    if results:
        print("   Top 5 similar items:")
        for i, item in enumerate(results[0]):
            print(f"   {i+1}. {item['entity'].get('title', 'N/A')} "
                  f"(category: {item['entity'].get('category', 'N/A')}, "
                  f"distance: {item['distance']:.4f})")

    print(f"\n📋 Step 7: Filtered Search")
    # Search with filter
    filtered_results = milvus.search_vectors(
        collection_name=collection_name,
        query_vectors=query_vectors,
        limit=3,
        filter_expr="category == 'technology'",
        output_fields=["title", "category", "score"]
    )

    if filtered_results:
        print("   Technology category results:")
        for i, item in enumerate(filtered_results[0]):
            print(f"   {i+1}. {item['entity'].get('title', 'N/A')} "
                  f"(distance: {item['distance']:.4f})")

    print(f"\n📋 Step 8: Delete Some Data")
    milvus.delete_data(collection_name, "id > 90")

    print(f"\n📋 Step 9: Final Collection Info")
    milvus.get_collection_info(collection_name)

    print(f"\n🎉 Demo completed successfully!")
    print("=" * 50)


if __name__ == "__main__":
    demo_basic_operations()
